{"sysTime":"2025-09-29 14:04:14.799","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-09-29 14:04:14.790","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:14.849","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:14.998","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:14.999","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-09-29 14:04:14.999","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-09-29 14:04:15.000","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-09-29 14:04:15.018","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-29 14:04:15.023","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-29 14:04:15.083","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:15.094","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-09-29 14:04:15.109","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-09-29 14:04:16.328","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:16.332","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=delivery-mysql.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:16.333","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=delivery-mysql.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-mysql.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:16.334","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-09-29 14:04:16.334","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=delivery-mysql.yaml, group=apply, tenant=test, config=spring:\\n  autoconfigure:\\n    #自动化配置 例外处理\\n    exclude: com.alibaba.druid.spring.boot.autoconfigure.Dr...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:17.543","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:17.545","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=delivery-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:17.546","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=delivery-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:17.547","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=delivery-config.yaml, group=apply, tenant=test, config=delivery-config:\\n  default-storage-code: Yunda \\n  default-express-corp-code: JD\\n  default-logistics-...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:18.768","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:18.770","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=common-starter-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:18.771","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=common-starter-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=common-starter-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:18.773","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=common-starter-config.yaml, group=apply, tenant=test, config=spring:\\n  redis:\\n    host: ************\\n    password: Qa@123yes\\n    port: 6379\\n    database: 15\\n  da...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:20.101","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:20.106","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=yunda-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)\n\tat java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:20.106","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=yunda-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=yunda-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:20.108","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=yunda-config.yaml, group=apply, tenant=test, config=yunda:\\n  appKey: 21177924\\n  customerId: YWKH000275\\n  warehouseCode: CK031\\n  format: xml\\n  signMethod...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:21.316","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:21.318","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=kd100-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:21.318","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=kd100-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=kd100-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:21.320","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=kd100-config.yaml, group=apply, tenant=test, config=kd100:\\n  appKey: \\\"omHFcvuP7026\\\"\\n  customer: \\\"A28D4B9D6BD6304A74EE0C2185351244\\\"\\n  schema: json\\n  resu...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:22.526","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:22.527","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=jd-config.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:22.527","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=jd-config.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=jd-config.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:22.528","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=jd-config.yaml, group=apply, tenant=test, config=jd:\\n  apiUrl: https://api.jdl.com\\n  accessToken: fd3d43f96f8a446aae472d6e3b5db0df\\n  appKey: 2723d005...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:23.731","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:23.732","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=apply-cos-tencent.properties, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:23.732","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=apply-cos-tencent.properties, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=apply-cos-tencent.properties&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:23.734","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=apply-cos-tencent.properties, group=apply, tenant=test, config=cos.tencent.secretId=AKIDmJZYHkKuc2D7k6i1dy1pLccLtEI5GfZ0\\ncos.tencent.secretKey=JMCl3wax3wZy7yDKAoCK...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:25.073","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:25.074","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=delivery-application, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:25.075","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=delivery-application, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:25.076","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=delivery-application, group=apply, tenant=test, config=\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:25.076","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:26.282","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:26.284","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=delivery-application.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:26.284","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=delivery-application.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:26.285","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=delivery-application.yaml, group=apply, tenant=test, config=server:\\n  port: 20130\\n  servlet:\\n    session:\\n      cookie:\\n        http-only: false\\n\\nspring:\\n  main...\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:27.498","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.http.ServerHttpAgent:115","methodName":"c.a.n.client.config.http.ServerHttpAgent:httpGet-115","message":"{\"msg\":\"[NACOS Exception httpGet] currentServerAddr: http://nacos.public:8848\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-test.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-test.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:27.499","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:205","methodName":"c.a.n.client.config.impl.ClientWorker:getServerConfig-205","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [sub-server] get server config exception, dataId=delivery-application-test.yaml, group=apply, tenant=test\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-test.yaml&tenant=test&group=apply\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-test.yaml&tenant=test&group=apply\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.get(NacosRestTemplate.java:93)\n\tat com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:99)\n\tat com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)\n\tat com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:200)\n"}
{"sysTime":"2025-09-29 14:04:27.499","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:174","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-174","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get from server error, dataId=delivery-application-test.yaml, group=apply, tenant=test, msg=ErrCode:500, ErrMsg:Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/cs/configs?dataId=delivery-application-test.yaml&tenant=test&group=apply\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:27.499","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.n.client.config.NacosConfigService:179","methodName":"c.a.n.client.config.NacosConfigService:getConfigInner-179","message":"{\"msg\":\"[fixed-nacos.public_8848-test] [get-config] get snapshot ok, dataId=delivery-application-test.yaml, group=apply, tenant=test, config=\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:27.499","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-test.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:27.500","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-test.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-09-29 14:04:27.508","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:27.509","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"test\"","thrown":""}
{"sysTime":"2025-09-29 14:04:28.153","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-09-29 14:04:28.154","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-09-29 14:04:28.179","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-09-29 14:04:28.362","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=8eb6ebee-7df7-3621-a2ed-bc44497ac6e4","thrown":""}
{"sysTime":"2025-09-29 14:04:28.573","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.575","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.577","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$838/0x000000030170b250] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.580","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.595","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.600","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.605","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.625","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.627","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:28.770","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-09-29 14:04:28.774","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-09-29 14:04:28.777","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-09-29 14:04:28.777","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-09-29 14:04:28.813","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-09-29 14:04:28.813","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1293 ms","thrown":""}
{"sysTime":"2025-09-29 14:04:30.664","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-09-29 14:04:32.127","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-09-29 14:04:33.593","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-09-29 14:04:35.046","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-09-29 14:04:36.329","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-5,db-delivery} inited","thrown":""}
{"sysTime":"2025-09-29 14:04:36.330","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-09-29 14:04:36.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-09-29 14:04:36.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-delivery] success","thrown":""}
{"sysTime":"2025-09-29 14:04:36.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-09-29 14:04:36.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-09-29 14:04:36.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [5] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-09-29 14:04:37.326","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:37.398","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:38.947","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-09-29 14:04:39.111","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-09-29 14:04:40.414","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-09-29 14:04:40.928","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:40.930","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:40.966","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-09-29 14:04:40.966","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-09-29 14:04:40.966","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-09-29 14:04:40.972","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-09-29 14:04:42.837","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:44.122","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:45.328","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:45.329","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:45.330","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:357","methodName":"com.alibaba.nacos.client.naming:updateServiceNow-357","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateServiceNow(HostReactor.java:355)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.getServiceInfo(HostReactor.java:330)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.subscribe(HostReactor.java:143)\n\tat com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:465)\n\tat com.alibaba.nacos.client.naming.NacosNamingService.subscribe(NacosNamingService.java:454)\n"}
{"sysTime":"2025-09-29 14:04:45.599","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.602","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.605","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.762","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-09-29 14:04:45.775","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.778","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.781","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.935","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-09-29 14:04:45.942","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.944","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:45.947","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:46.130","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-09-29 14:04:46.349","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:46.496","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-09-29 14:04:46.506","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_risk_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:46.509","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:46.511","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:04:46.655","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_risk_group, nameServerAddr=name-service:9876, topic=ets_java_risk_task_topic, tag=queueRiskTask","thrown":""}
{"sysTime":"2025-09-29 14:04:47.710","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:48.102","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.116","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-09-29 14:04:48.266","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-09-29 14:04:48.281","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-09-29 14:04:48.283","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='***********', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-09-29 14:04:48.283","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] test registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='***********', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.824","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.824","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.825","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.825","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:48.937","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:50.233","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:50.234","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:50.234","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-09-29 14:04:50.510","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:51.791","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:53.051","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:53.051","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:53.066","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:81","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-81","message":"{\"msg\":\"nacos registry, delivery-application register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='nacos.public:8848', username='', password='', endpoint='', namespace='test', watchDelay=30000, logName='', service='delivery-application', weight=1.0, clusterName='DEFAULT', group='apply', namingLoadCacheAtStart='false', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='***********', networkInterface='', port=20130, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null, instanceEnabled=true, ephemeral=true, failureToleranceEnabled=false}, ipDeleteTimeout=null, failFast=true}},\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance?app=unknown&groupName=apply&metadata=%7B%22preserved.register.source%22%3A%22SPRING_CLOUD%22%7D&namespaceId=test&port=20130&enable=true&healthy=true&clusterName=DEFAULT&ip=***********&weight=1.0&ephemeral=true&serviceName=apply%40%40delivery-application\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:254)\n\tat com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:220)\n\tat com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:75)\n\tat org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:264)\n\tat com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:78)\n\tat org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:156)\n\tat org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:119)\n"}
{"sysTime":"2025-09-29 14:04:53.447","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:54.502","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:54.680","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:632","methodName":"o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:refresh-632","message":"{\"msg\":\"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:54.690","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-09-29 14:04:54.733","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:55.710","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:56.033","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:56.034","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/list failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:56.034","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:502","methodName":"com.alibaba.nacos.client.naming:run-502","message":"{\"msg\":\"[NA] failed to update serviceName: apply@@delivery-application\"}","thrown":"com.alibaba.nacos.api.exception.NacosException: failed to req API:/nacos/v1/ns/instance/list after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/list?app=unknown&healthyOnly=false&namespaceId=test&clientIP=127.0.0.1&serviceName=apply%40%40delivery-application&udpPort=53051&clusters=\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:564)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.queryList(NamingProxy.java:415)\n\tat com.alibaba.nacos.client.naming.core.HostReactor.updateService(HostReactor.java:392)\n\tat com.alibaba.nacos.client.naming.core.HostReactor$UpdateTask.run(HostReactor.java:475)\n\tat java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\n\tat java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)\n"}
{"sysTime":"2025-09-29 14:04:56.920","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:634","methodName":"com.alibaba.nacos.client.naming:callServer-634","message":"{\"msg\":\"[NA] failed to request cause of unknown reason\"}","thrown":"java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/jdk.internal.reflect.GeneratedConstructorAccessor9.newInstance(Unknown Source)\n\tat java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2094)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection$10.run(HttpURLConnection.java:2089)\n\tat java.base/java.security.AccessController.doPrivileged(AccessController.java:569)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getChainedException(HttpURLConnection.java:2088)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1646)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\nCaused by: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:2037)\n\tat java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1626)\n\tat java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:529)\n\tat com.alibaba.nacos.common.http.client.response.JdkHttpClientResponse.getStatusCode(JdkHttpClientResponse.java:79)\n\tat com.alibaba.nacos.common.http.client.handler.AbstractResponseHandler.handle(AbstractResponseHandler.java:43)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.execute(NacosRestTemplate.java:483)\n\tat com.alibaba.nacos.common.http.client.NacosRestTemplate.exchangeForm(NacosRestTemplate.java:427)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.callServer(NamingProxy.java:611)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:534)\n\tat com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:506)\n"}
{"sysTime":"2025-09-29 14:04:56.921","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:560","methodName":"com.alibaba.nacos.client.naming:reqApi-560","message":"{\"msg\":\"request: /nacos/v1/ns/instance/beat failed, servers: [nacos.public:8848], code: 500, msg: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:56.921","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:197","methodName":"com.alibaba.nacos.client.naming:run-197","message":"{\"msg\":\"[CLIENT-BEAT] failed to send beat: {\\\"port\\\":20130,\\\"ip\\\":\\\"***********\\\",\\\"weight\\\":1.0,\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"cluster\\\":\\\"DEFAULT\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"scheduled\\\":false,\\\"period\\\":5000,\\\"stopped\\\":false}, code: 500, msg: failed to req API:/nacos/v1/ns/instance/beat after all servers([nacos.public:8848]) tried: java.io.IOException: Server returned HTTP response code: 502 for URL: http://nacos.public:8848/nacos/v1/ns/instance/beat?app=unknown&serviceName=apply%40%40delivery-application&namespaceId=test&port=20130&clusterName=DEFAULT&ip=***********\"}","thrown":""}
{"sysTime":"2025-09-29 14:04:56.923","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-09-29 14:04:56.923","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"74940","thread":"main","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-09-29 14:05:03.093","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-09-29 14:05:03.088","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.127","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.260","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-09-29 14:05:03.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-09-29 14:05:03.262","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-09-29 14:05:03.274","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-29 14:05:03.282","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-09-29 14:05:03.337","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.345","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-09-29 14:05:03.358","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-09-29 14:05:03.523","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-09-29 14:05:03.750","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.811","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-test.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.812","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-test.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-09-29 14:05:03.819","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:03.820","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"test\"","thrown":""}
{"sysTime":"2025-09-29 14:05:04.409","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-09-29 14:05:04.410","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-09-29 14:05:04.430","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-09-29 14:05:04.602","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=8eb6ebee-7df7-3621-a2ed-bc44497ac6e4","thrown":""}
{"sysTime":"2025-09-29 14:05:04.829","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.832","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.833","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$836/0x000000f8017071b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.836","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.849","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.855","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.860","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.881","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:04.883","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:05.014","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-09-29 14:05:05.018","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-09-29 14:05:05.020","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-09-29 14:05:05.020","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-09-29 14:05:05.049","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-09-29 14:05:05.049","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1218 ms","thrown":""}
{"sysTime":"2025-09-29 14:05:06.909","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-09-29 14:05:08.324","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-09-29 14:05:09.798","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-09-29 14:05:11.286","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-09-29 14:05:12.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-5,db-delivery} inited","thrown":""}
{"sysTime":"2025-09-29 14:05:12.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-09-29 14:05:12.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-09-29 14:05:12.541","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-delivery] success","thrown":""}
{"sysTime":"2025-09-29 14:05:12.542","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-09-29 14:05:12.542","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-09-29 14:05:12.542","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [5] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-09-29 14:05:13.670","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:13.733","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:15.348","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-09-29 14:05:15.501","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-09-29 14:05:16.791","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-09-29 14:05:17.289","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.291","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.328","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-09-29 14:05:17.328","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-09-29 14:05:17.328","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-09-29 14:05:17.334","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-09-29 14:05:17.417","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-09-29 14:05:17.421","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-09-29 14:05:17.660","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.663","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.665","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.840","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-09-29 14:05:17.853","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.855","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:17.858","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.018","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-09-29 14:05:18.025","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.028","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.031","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.185","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-09-29 14:05:18.339","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-09-29 14:05:18.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.483","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-09-29 14:05:18.498","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_risk_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.501","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.504","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-09-29 14:05:18.651","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_risk_group, nameServerAddr=name-service:9876, topic=ets_java_risk_task_topic, tag=queueRiskTask","thrown":""}
{"sysTime":"2025-09-29 14:05:19.332","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-09-29 14:05:19.344","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-09-29 14:05:19.465","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.469","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-09-29 14:05:19.469","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='***********', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-09-29 14:05:19.470","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] test registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='***********', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-09-29 14:05:19.499","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:76","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-76","message":"nacos registry, apply delivery-application ***********:20130 register finished","thrown":""}
{"sysTime":"2025-09-29 14:05:19.545","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.e.d.application.DeliveryApplication:56","methodName":"c.e.d.application.DeliveryApplication:logStarted-56","message":"Started DeliveryApplication in 16.733 seconds (process running for 17.068)","thrown":""}
{"sysTime":"2025-09-29 14:05:19.550","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] common-starter-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.550","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=common-starter-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=common-starter-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] jd-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=jd-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=jd-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application-test.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application-test.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application-test.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.551","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] yunda-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=yunda-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.552","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=yunda-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] kd100-config.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=kd100-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=kd100-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-application.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-test] [subscribe] delivery-mysql.yaml+apply+test","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-test] [add-listener] ok, tenant=test, dataId=delivery-mysql.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-09-29 14:05:19.553","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-mysql.yaml, group=apply","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:resetTimeoutAfterSalesReviewsHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6d6b2fbc[class com.ets.delivery.application.app.job.AfterSalesReviewsJob$$SpringCGLIB$$0#resetTimeoutAfterSalesReviewsHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryAfterSalesReviewsNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71ad307d[class com.ets.delivery.application.app.job.AfterSalesReviewsJob$$SpringCGLIB$$0#retryAfterSalesReviewsNotifyHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:manualNotifyAfterSalesReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a01bf0e[class com.ets.delivery.application.app.job.AfterSalesReviewsJob$$SpringCGLIB$$0#manualNotifyAfterSalesReviewHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:erpOrderDailyCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@101d33a7[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#erpOrderDailyCheckHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixErpOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@24bff8d7[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#fixErpOrderHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:expressSubscribe, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3c090ae5[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#expressSubscribe]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.562","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaExpressHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1b572ba5[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#yundaExpressHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.563","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaExpressCacheQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@62aea363[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#yundaExpressCacheQueryHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.563","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:LogisticsQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@390c96a1[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#logisticsQueryHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.563","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaLogisticsExpressQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4da6fd47[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#yundaLogisticsExpressQueryHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.565","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixLogisticsExpressStatusHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f0efd55[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#fixLogisticsExpressStatusHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.565","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpAutoCancelHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@78acee6c[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpAutoCancelHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.565","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpQueryTraceInfoHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@25c8d646[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpQueryTraceInfoHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:postReviewReleaseHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5ffe5b7d[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#postReviewReleaseHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:initPostReviewDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4782e182[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#initPostReviewDataHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewDateSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@58c2a890[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewDateSummaryHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewUserSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3bdb3f28[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewUserSummaryHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyReviewOrderRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1653ae9d[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#applyReviewOrderRiskHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:releaseUserRiskReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@460dc324[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#releaseUserRiskReviewHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:checkOvertimeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@bc6655d[class com.ets.delivery.application.app.job.SendBackJob$$SpringCGLIB$$0#checkOvertimeHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:logisticsAvgHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69063972[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#logisticsAvgHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:goodsStockHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@622a4589[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#goodsStockHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageAlarmFileHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4a16ae0b[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#storageAlarmFileHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.566","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:stockOutSkuHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72c2d699[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#stockOutSkuHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.567","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageMapAddressCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2ad7ac1a[class com.ets.delivery.application.app.job.StorageMapJob$$SpringCGLIB$$0#storageMa1pAddressCheck]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.567","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a61a2c4[class com.ets.delivery.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.600","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@25db0f54[class com.ets.risk.application.app.job.RiskTaskJob$$SpringCGLIB$$0#reExecRiskHandler]","thrown":""}
{"sysTime":"2025-09-29 14:05:19.704","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-09-29 14:05:19.886","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-09-29 14:05:19.892","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"Thread-36","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20132","thrown":""}
{"sysTime":"2025-09-29 14:05:19.933","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"RMI TCP Connection(6)-127.0.0.1","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-09-29 14:05:19.933","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"RMI TCP Connection(6)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:532","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-532","message":"Initializing Servlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-09-29 14:05:19.934","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"RMI TCP Connection(6)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:554","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-554","message":"Completed initialization in 1 ms","thrown":""}
{"sysTime":"2025-09-29 14:05:28.492","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"***********#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"***********\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-09-29 14:05:28.496","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='***********#20130#DEFAULT#apply@@delivery-application', ip='***********', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}, Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-09-29 14:05:28.497","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"75016","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(2) service: apply@@delivery-application -> [{\"instanceId\":\"***********#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"***********\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000},{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-09-29 14:10:53.670","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"58f03e4d-2076-44c0-9e9d-68295c7776ad","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"1044\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"content-type\":\"application/xml\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"acc8b3ab0cb4f3f7f3985a1006648d02\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/notify/yunda/expressPush\",\"params\":{},\"postData\":\"<?xml version=\\\\\\\"1.0\\\\\\\" encoding=\\\\\\\"UTF-8\\\\\\\"?><request>    <sourceOrder>11</sourceOrder>    <abnormalCause/>    <branchPhone>0770-6137726</branchPhone>    <currentStatus>25</currentStatus>    <operateTime>2025-09-29 12:51:46</operateTime>    <customerCode>YWKH000275</customerCode>    <branchName>泽沣民宿快递代收点</branchName>    <description>【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********</description>    <deliveryOrderCode>2509271221028720150</deliveryOrderCode>    <warehouseCode>CK031</warehouseCode>    <modeType>B2C</modeType>    <empPhone/>    <logisticsCompanyName>ZTO</logisticsCompanyName>    <empName/>    <expressCode>78943341433600</expressCode>    <omsOrderStatus>已签收</omsOrderStatus>    <waybillNo>78943341433600</waybillNo></request>\"}","thrown":""}
{"sysTime":"2025-09-29 14:10:53.753","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"58f03e4d-2076-44c0-9e9d-68295c7776ad","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.GlobalExceptionHandler:138","methodName":"c.e.s.interceptor.GlobalExceptionHandler:systemExceptionHandler-138","message":"{\"userId\":\"\",\"url\":\"/notify/yunda/expressPush\",\"msg\":\"系统错误：Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\\r\\n#org.springframework.http.converter.xml.AbstractXmlHttpMessageConverter.readInternal(AbstractXmlHttpMessageConverter.java:78)\\r\\n#org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:198)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:161)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:135)\\r\\n#org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\\r\\n#org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\\r\\n#org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\\r\\n#org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)\\r\\n#org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.RepeatedlyReadFilter.doFilter(RepeatedlyReadFilter.java:52)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.HttpServletResponseFilter.doFilter(HttpServletResponseFilter.java:37)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)\\r\\n#org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)\\r\\n#org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)\\r\\n#org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)\\r\\n#org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)\\r\\n#org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\\r\\n#org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)\\r\\n#org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)\\r\\n#org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)\\r\\n#org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)\\r\\n#org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)\\r\\n#org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\\r\\n#org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)\\r\\n#java.base/java.lang.Thread.run(Thread.java:840)\"}","thrown":""}
{"sysTime":"2025-09-29 14:10:54.152","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"58f03e4d-2076-44c0-9e9d-68295c7776ad","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-1","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"EmailJobBean\",\"className\":\"com.ets.starter.disposer.EmailDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"content\":\"Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\\r\\n#org.springframework.http.converter.xml.AbstractXmlHttpMessageConverter.readInternal(AbstractXmlHttpMessageConverter.java:78)\\r\\n#org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:198)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:161)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:135)\\r\\n#org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\\r\\n#org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\\r\\n#org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\\r\\n#org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)\\r\\n#org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.RepeatedlyReadFilter.doFilter(RepeatedlyReadFilter.java:52)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.HttpServletResponseFilter.doFilter(HttpServletResponseFilter.java:37)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)\\r\\n#org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)\\r\\n#org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)\\r\\n#org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)\\r\\n#org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)\\r\\n#org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\\r\\n#org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)\\r\\n#org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)\\r\\n#org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)\\r\\n#org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)\\r\\n#org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)\\r\\n#org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\\r\\n#org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)\\r\\n#java.base/java.lang.Thread.run(Thread.java:840)\\r\\n#requestInfo: {\\\"header\\\":{\\\"referer\\\":\\\"https://gdmg-dev.etczs.net/user/product\\\",\\\"content-length\\\":\\\"1044\\\",\\\"host\\\":\\\"127.0.0.1:20130\\\",\\\"appcode\\\":\\\"UnifinedPlatform\\\",\\\"logincode\\\":\\\"huanghaojie\\\",\\\"content-type\\\":\\\"application/xml\\\",\\\"connection\\\":\\\"keep-alive\\\",\\\"accept-encoding\\\":\\\"gzip, deflate, br\\\",\\\"token\\\":\\\"acc8b3ab0cb4f3f7f3985a1006648d02\\\",\\\"user-agent\\\":\\\"Apifox/1.0.0 (https://apifox.com)\\\",\\\"accept\\\":\\\"*/*\\\"},\\\"requestUri\\\":\\\"/notify/yunda/expressPush\\\",\\\"params\\\":{},\\\"postData\\\":\\\"<?xml version=\\\\\\\\\\\\\\\"1.0\\\\\\\\\\\\\\\" encoding=\\\\\\\\\\\\\\\"UTF-8\\\\\\\\\\\\\\\"?><request>    <sourceOrder>11</sourceOrder>    <abnormalCause/>    <branchPhone>0770-6137726</branchPhone>    <currentStatus>25</currentStatus>    <operateTime>2025-09-29 12:51:46</operateTime>    <customerCode>YWKH000275</customerCode>    <branchName>泽沣民宿快递代收点</branchName>    <description>【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********</description>    <deliveryOrderCode>2509271221028720150</deliveryOrderCode>    <warehouseCode>CK031</warehouseCode>    <modeType>B2C</modeType>    <empPhone/>    <logisticsCompanyName>ZTO</logisticsCompanyName>    <empName/>    <expressCode>78943341433600</expressCode>    <omsOrderStatus>已签收</omsOrderStatus>    <waybillNo>78943341433600</waybillNo></request>\\\"}\",\"title\":\"系统错误：Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\"},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"58f03e4d-2076-44c0-9e9d-68295c7776ad\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-09-29 14:10:54.245","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"58f03e4d-2076-44c0-9e9d-68295c7776ad","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-1","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-2,\"msg\":\"Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\",\"data\":null}","thrown":""}
{"sysTime":"2025-09-29 14:11:11.523","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"89a8c713-9db4-481c-bf3b-1eaeea82603f","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-2","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"1082\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"content-type\":\"application/xml\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"acc8b3ab0cb4f3f7f3985a1006648d02\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/notify/yunda/expressPush\",\"params\":{},\"postData\":\"<?xml version=\\\\\\\"1.0\\\\\\\" encoding=\\\\\\\"UTF-8\\\\\\\"?><request>    <sourceOrder>11</sourceOrder>    <abnormalCause/>    <branchPhone>0770-6137726</branchPhone>    <currentStatus>25</currentStatus>    <operateTime>2025-09-29 12:51:46</operateTime>    <customerCode>YWKH000275</customerCode>    <branchName>泽沣民宿快递代收点</branchName>    <description>【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********</description>    <deliveryOrderCode>2509271221028720150</deliveryOrderCode>    <warehouseCode>CK031</warehouseCode>    <modeType>B2C</modeType>    <empPhone/>    <logisticsCompanyName>ZTO</logisticsCompanyName>    <empName/>    <expressCode>78943341433600</expressCode>    <omsOrderStatus>已签收</omsOrderStatus>    <waybillNo>78943341433600</waybillNo></request>\"}","thrown":""}
{"sysTime":"2025-09-29 14:11:11.527","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"89a8c713-9db4-481c-bf3b-1eaeea82603f","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-2","className":"c.e.s.interceptor.GlobalExceptionHandler:138","methodName":"c.e.s.interceptor.GlobalExceptionHandler:systemExceptionHandler-138","message":"{\"userId\":\"\",\"url\":\"/notify/yunda/expressPush\",\"msg\":\"系统错误：Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\\r\\n#org.springframework.http.converter.xml.AbstractXmlHttpMessageConverter.readInternal(AbstractXmlHttpMessageConverter.java:78)\\r\\n#org.springframework.http.converter.AbstractHttpMessageConverter.read(AbstractHttpMessageConverter.java:198)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:185)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:161)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:135)\\r\\n#org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\\r\\n#org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\\r\\n#org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\\r\\n#org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)\\r\\n#org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.RepeatedlyReadFilter.doFilter(RepeatedlyReadFilter.java:52)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.HttpServletResponseFilter.doFilter(HttpServletResponseFilter.java:37)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)\\r\\n#org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)\\r\\n#org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)\\r\\n#org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)\\r\\n#org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)\\r\\n#org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\\r\\n#org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)\\r\\n#org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)\\r\\n#org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)\\r\\n#org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)\\r\\n#org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)\\r\\n#org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\\r\\n#org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)\\r\\n#java.base/java.lang.Thread.run(Thread.java:840)\"}","thrown":""}
{"sysTime":"2025-09-29 14:11:11.698","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"89a8c713-9db4-481c-bf3b-1eaeea82603f","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-2","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-2,\"msg\":\"Could not unmarshal to [class com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO]: jakarta.xml.bind.UnmarshalException\\n - with linked exception:\\n[org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 15; XML 声明中 \\\"version\\\" 后面跟随的值必须是用引号括起来的字符串。]\",\"data\":null}","thrown":""}
{"sysTime":"2025-09-29 14:11:54.714","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f24fa565-44dd-4616-9513-780e9181d92e","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-3","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"1078\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"content-type\":\"application/xml\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"acc8b3ab0cb4f3f7f3985a1006648d02\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/notify/yunda/expressPush\",\"params\":{},\"postData\":\"<?xml version=\\\"1.0\\\" encoding=\\\"UTF-8\\\"?><request>    <sourceOrder>11</sourceOrder>    <abnormalCause/>    <branchPhone>0770-6137726</branchPhone>    <currentStatus>25</currentStatus>    <operateTime>2025-09-29 12:51:46</operateTime>    <customerCode>YWKH000275</customerCode>    <branchName>泽沣民宿快递代收点</branchName>    <description>【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********</description>    <deliveryOrderCode>2509271221028720150</deliveryOrderCode>    <warehouseCode>CK031</warehouseCode>    <modeType>B2C</modeType>    <empPhone/>    <logisticsCompanyName>ZTO</logisticsCompanyName>    <empName/>    <expressCode>78943341433600</expressCode>    <omsOrderStatus>已签收</omsOrderStatus>    <waybillNo>78943341433600</waybillNo></request>\"}","thrown":""}
{"sysTime":"2025-09-29 14:11:54.773","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f24fa565-44dd-4616-9513-780e9181d92e","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-3","className":"c.e.d.a.c.notify.YundaController:93","methodName":"c.e.d.a.c.notify.YundaController:express-93","message":"【物流轨迹推送】【韵达】YundaExpressNotifyDTO(sourceOrder=11, modeType=B2C, abnormalCause=, branchPhone=0770-6137726, operateTime=2025-09-29 12:51:46, customerCode=YWKH000275, branchName=泽沣民宿快递代收点, description=【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********, deliveryOrderCode=2509271221028720150, warehouseCode=CK031, empPhone=, logisticsCompanyName=ZTO, empName=, expressCode=78943341433600, omsOrderStatus=已签收)","thrown":""}
{"sysTime":"2025-09-29 14:11:54.780","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f24fa565-44dd-4616-9513-780e9181d92e","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-3","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"ExpressNotifyJobBean\",\"className\":\"com.ets.delivery.application.app.disposer.ExpressNotifyDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"expressCode\":\"Yunda\",\"notifyData\":{\"abnormalCause\":\"\",\"branchName\":\"泽沣民宿快递代收点\",\"branchPhone\":\"0770-6137726\",\"customerCode\":\"YWKH000275\",\"deliveryOrderCode\":\"2509271221028720150\",\"description\":\"【防城港市】快件已在 代收点 的【泽沣民宿快递代收点】暂存，【取件地址：防城港市港口区公车镇沙潭江街道\uD844\uDF36港二区10排12号，营业时间09:00-21:00】，请及时取件。如有疑问请联系业务员：***********，代理点电话：***********，投诉电话：***********\",\"empName\":\"\",\"empPhone\":\"\",\"expressCode\":\"78943341433600\",\"logisticsCompanyName\":\"ZTO\",\"modeType\":\"B2C\",\"omsOrderStatus\":\"已签收\",\"operateTime\":\"2025-09-29 12:51:46\",\"sourceOrder\":11,\"warehouseCode\":\"CK031\"}},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"f24fa565-44dd-4616-9513-780e9181d92e\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-09-29 14:11:54.852","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f24fa565-44dd-4616-9513-780e9181d92e","spanId":"0","parentId":"","exportable":"","pid":"75016","thread":"http-nio-20130-exec-3","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><response><code>200</code><flag>success</flag><message>成功</message></response>","thrown":""}
