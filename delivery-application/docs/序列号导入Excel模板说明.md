# 序列号导入Excel模板说明

## 文件格式要求
- 支持 `.xls` 和 `.xlsx` 格式
- 第一行为表头，从第二行开始为数据

## Excel表头格式（按顺序）

| 列序号 | 字段名称 | 是否必填 | 说明 | 示例值 |
|--------|----------|----------|------|--------|
| A列 | 序列号 | 是 | 设备的唯一序列号 | SN123456789 |
| B列 | 业务单号 | 否 | 发货单号、入库单号等 | DO202509160001 |
| C列 | 仓库SKU编码 | 否 | 仓库中的SKU编码 | SKU001 |
| D列 | 库存类型 | 否 | ZP/CC/JS/XS等 | ZP |
| E列 | 仓库编码 | 否 | 仓库代码 | WH001 |
| F列 | 业务来源 | 否 | 业务来源标识 | ERP |
| G列 | 序列号类型 | 否 | RETURN/STOCK_IN/STOCK_OUT/DELIVERY | DELIVERY |
| H列 | 备注 | 否 | 其他说明信息 | 批量导入 |

## 序列号类型说明
- `RETURN`: 寄回
- `STOCK_IN`: 入库  
- `STOCK_OUT`: 出库
- `DELIVERY`: 发货

## 库存类型说明
- `ZP`: 正品
- `CC`: 次品
- `JS`: 借样
- `XS`: 销售

## 导入流程
1. 上传Excel文件
2. 系统解析文件内容
3. 数据校验和预处理
4. 按序列号类型分组处理
5. 批量处理序列号操作
6. 返回处理结果

## 注意事项
- 序列号字段为必填项，不能为空
- 序列号类型如果为空，系统将跳过处理
- 建议单次导入数量不超过10000条
- 导入过程中如有错误，可查看错误日志获取详细信息