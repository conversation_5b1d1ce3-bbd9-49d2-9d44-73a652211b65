package com.ets.delivery.application.app.factory.express.impl;

import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.express.ExpressFactory;
import com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO;
import com.ets.delivery.application.common.bo.express.ExpressDataBO;
import com.ets.delivery.application.common.bo.express.ExpressLogBO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeStatusEnum;
import com.ets.delivery.application.common.consts.yunda.YundaExpressOrderStatusEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.delivery.application.infra.service.ExpressLogService;
import com.ets.delivery.application.infra.service.ExpressService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class YundaExpressManage extends ExpressBase {

    @Autowired
    private ExpressService expressService;

    @Autowired
    private ExpressLogService expressLogService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 处理韵达快递通知回调
     * 
     * @param notifyDTO 快递通知数据
     * @return 更新后的快递信息
     */
    @Override
    public Express expressNotify(ExpressNotifyDTO notifyDTO) {
        YundaExpressNotifyDTO yundaExpressNotifyDTO = ConverterRegistry.getInstance().convert(
                YundaExpressNotifyDTO.class, notifyDTO.getNotifyData());

        if (ObjectUtil.isNull(yundaExpressNotifyDTO)) {
            log.error("韵达物流轨迹推送数据转换失败，原始数据: {}", notifyDTO.getNotifyData());
            ToolsHelper.throwException("物流轨迹推送数据格式不正确");
        }

        String expressCode = yundaExpressNotifyDTO.getExpressCode();
        String redisKey = "delivery:express:yunda:track:" + expressCode;
        try {
            // 将原始轨迹数据追加到Redis List
            String trackJson = JSON.toJSONString(yundaExpressNotifyDTO);
            stringRedisTemplate.opsForList().rightPush(redisKey, trackJson);
            stringRedisTemplate.expire(redisKey, 7, TimeUnit.DAYS);

            log.info("韵达轨迹已缓存到Redis，单号：{}，内容：{}", expressCode, trackJson);
            // 这里不再直接处理轨迹，返回null或自定义响应
            return null;
        } catch (Exception e) {
            log.error("缓存韵达快递轨迹到Redis异常，快递单号: {}, 错误: {}", expressCode, e.getMessage(), e);
            ToolsHelper.throwException("缓存韵达快递轨迹到Redis时发生错误：" + e.getMessage());
            return null;
        }
    }

    @Override
    public Express expressQuery(Express express) {
        express.setExpressCompany("");

        // 韵达没有查询接口 使用快递100
        return ExpressFactory.create(ExpressCodeEnum.KD100.getValue()).expressQuery(express);
    }

    /**
     * 更新快递信息
     * 
     * @param notifyDTO 韵达通知数据
     * @return 更新后的快递信息，记录不存在时返回null
     */
    public Express updateExpressInfo(YundaExpressNotifyDTO notifyDTO) {
        if (ObjectUtil.isNull(notifyDTO) || StringUtils.isEmpty(notifyDTO.getExpressCode())) {
            log.error("无效的通知数据");
            ToolsHelper.throwException("无效的通知数据");
        }
        
        try {
            // 1. 获取快递记录
            Express express = expressService.getOneByExpressNumber(notifyDTO.getExpressCode());
            
            // 记录不存在直接返回null
            if (ObjectUtils.isNull(express)) {
                log.info("快递记录不存在，跳过处理，单号：{}", notifyDTO.getExpressCode());
                return null;
            }
            
            // 非韵达轨迹不更新
            if (!express.getExpressCode().equals(ExpressCodeEnum.YUNDA.getValue())) {
                log.info("非韵达快递轨迹，跳过更新，单号：{}", notifyDTO.getExpressCode());
                return express;
            }
            
            // 2. 处理轨迹数据
            List<ExpressDataBO> expressDataList = getAndUpdateTrackingData(express, notifyDTO);
            
            // 3. 更新快递状态
            updateExpressState(express, expressDataList, notifyDTO);
            
            // 4. 保存并记录日志
            saveExpressAndLog(express);
            
            return express;
        } catch (Exception e) {
            log.error("更新快递信息失败，单号：{}，错误：{}", notifyDTO.getExpressCode(), e.getMessage(), e);
            ToolsHelper.throwException("更新快递信息失败：" + e.getMessage());
            return null; // 这行代码不会执行，但为了编译通过
        }
    }

    /**
     * 获取并更新轨迹数据
     */
    private List<ExpressDataBO> getAndUpdateTrackingData(Express express, YundaExpressNotifyDTO notifyDTO) {
        // 解析现有轨迹数据
        List<ExpressDataBO> expressDataList = new ArrayList<>();
        if (StringUtils.isNotEmpty(express.getData())) {
            try {
                expressDataList = JSON.parseObject(express.getData(), new TypeReference<>() {});
            } catch (Exception e) {
                log.error("解析轨迹数据失败，将重新创建，单号：{}, 错误：{}", express.getExpressNumber(), e.getMessage());
                // 解析失败时返回空列表，重新开始收集轨迹
                expressDataList = new ArrayList<>();
            }
        }
        
        // 创建新的轨迹数据
        ExpressDataBO newTrackData = new ExpressDataBO();
        newTrackData.setFtime(notifyDTO.getOperateTime());
        newTrackData.setContext(notifyDTO.getDescription());
        newTrackData.setAreaName(notifyDTO.getBranchName());
        newTrackData.setStatus(notifyDTO.getOmsOrderStatus());
        
        // 检查是否存在相同的轨迹记录
        boolean exists = expressDataList.stream()
                .anyMatch(data -> data.getFtime().equals(newTrackData.getFtime()) && 
                         data.getContext().equals(newTrackData.getContext()));
        
        // 如果不存在相同轨迹，则添加
        if (!exists) {
            expressDataList.add(newTrackData);
            log.info("添加新轨迹，单号：{}，时间：{}", 
                    express.getExpressNumber(), notifyDTO.getOperateTime());
        }
        
        // 按时间降序排序
        expressDataList.sort((t1, t2) -> t2.getFtime().compareTo(t1.getFtime()));
        express.setData(JSON.toJSONString(expressDataList));
        
        return expressDataList;
    }

    /**
     * 更新快递状态信息
     */
    private void updateExpressState(Express express, List<ExpressDataBO> expressDataList, 
            YundaExpressNotifyDTO notifyDTO) {
        if (expressDataList.isEmpty()) {
            return;
        }
        
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // 更新基本信息
        express.setExpressCompany(notifyDTO.getLogisticsCompanyName().toLowerCase());
        express.setUpdatedAt(LocalDateTime.now());
        
        // 处理特殊状态（退回、拒收）
        Integer pushState = YundaExpressOrderStatusEnum.stateMap.getOrDefault(
                notifyDTO.getOmsOrderStatus(), ExpressStateEnum.ON_THE_WAY.getValue());
                
        if (Arrays.asList(ExpressStateEnum.SEND_BACK.getValue(), 
                ExpressStateEnum.RECEIVER_REJECT.getValue()).contains(pushState)) {
            express.setIsBack(1);
            
            LocalDateTime operateDateTime = LocalDateTime.parse(notifyDTO.getOperateTime(), dtf);
            if (express.getBackTime() == null || operateDateTime.isAfter(express.getBackTime())) {
                express.setBackTime(operateDateTime);
            }
        }
        
        // 获取最新轨迹和最早轨迹
        ExpressDataBO lastExpress = expressDataList.get(0);
        ExpressDataBO firstExpress = expressDataList.get(expressDataList.size() - 1);
        
        // 更新状态
        String orderStatus = lastExpress.getStatus();
        Integer state = YundaExpressOrderStatusEnum.stateMap.getOrDefault(
                orderStatus, ExpressStateEnum.ON_THE_WAY.getValue());
        express.setState(state);
        
        // 处理签收状态
        if (orderStatus.equals(YundaExpressOrderStatusEnum.SIGNED.getValue())) {
            express.setSubscribeStatus(ExpressSubscribeStatusEnum.SUBSCRIBE_SHUTDOWN.getValue());
            express.setReceivedTime(LocalDateTime.parse(lastExpress.getFtime(), dtf));
        }
        
        // 更新首条轨迹信息
        if (StringUtils.isEmpty(express.getFirstContext()) ||
                !express.getFirstContext().equals(firstExpress.getContext())) {
            express.setFirstArea(firstExpress.getAreaName());
            express.setFirstStatus(firstExpress.getStatus());
            express.setFirstContext(firstExpress.getContext());
            express.setFirstExpressTime(LocalDateTime.parse(firstExpress.getFtime(), dtf));
        }
        
        // 更新最新轨迹信息
        if (StringUtils.isEmpty(express.getLastContext()) || 
                !express.getLastContext().equals(lastExpress.getContext())) {
            express.setLastArea(lastExpress.getAreaName());
            express.setLastStatus(orderStatus);
            express.setLastContext(lastExpress.getContext());
            express.setLastExpressTime(LocalDateTime.parse(lastExpress.getFtime(), dtf));
        }
    }

    /**
     * 保存快递信息并记录日志
     */
    private void saveExpressAndLog(Express express) {
        // 保存快递信息，假设id不会为null
        expressService.updateById(express);
        
        // 记录日志
        ExpressLogBO logBO = new ExpressLogBO();
        logBO.setExpressSn(express.getExpressSn());
        logBO.setExpressNumber(express.getExpressNumber());
        logBO.setSubscribeStatus(express.getSubscribeStatus());
        
        // 记录完整的快递信息
        logBO.setContent("物流订阅回调修改:" + JSON.toJSONString(express));
        expressLogService.addLog(logBO);
    }
}
