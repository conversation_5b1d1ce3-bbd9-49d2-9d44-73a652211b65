package com.ets.delivery.application.app.job;

import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.app.thirdservice.business.KdBusiness;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.factory.express.impl.YundaExpressManage;
import com.ets.delivery.application.app.factory.express.strategy.YundaOrderStrategyManager;
import com.ets.delivery.application.app.thirdservice.request.yunda.YundaExpressNotifyDTO;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.infra.entity.Express;
import com.ets.redisson.template.DistributedLockTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.xxl.job.core.log.XxlJobLogger;

import java.util.*;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class ExpressJob {

    @Autowired
    private KdBusiness kdBusiness;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private YundaExpressManage yundaExpressManage;

    @Autowired
    private DistributedLockTemplate distributedLockTemplate;

    @Autowired
    private YundaOrderStrategyManager strategyManager;

    @Autowired
    private ExpressBusiness expressBusiness;

    @XxlJob("expressSubscribe")
    public ReturnT<String> expressSubscribe(String params){

        ExpressBO expressBO = JSON.parseObject(params, ExpressBO.class);

        kdBusiness.subscribe(expressBO);

        return ReturnT.SUCCESS;
    }

    /**
     * XXL-Job定时任务：韵达仓物流轨迹处理
     */
    @XxlJob("yundaExpressHandler")
    public ReturnT<String> yundaExpressHandler(String params) {
        // 查询所有相关的key：原始轨迹key和processing key
        Set<String> originalKeys = stringRedisTemplate.keys("delivery:express:yunda:track:*");
        Set<String> processingKeys = stringRedisTemplate.keys("delivery:express:yunda:track:*:processing");
        
        // 合并所有需要处理的key
        Set<String> allKeys = new HashSet<>();
        if (originalKeys != null) {
            allKeys.addAll(originalKeys);
        }
        if (processingKeys != null) {
            allKeys.addAll(processingKeys);
        }
        
        if (allKeys.isEmpty()) {
            return ReturnT.SUCCESS;
        }
        
        for (String key : allKeys) {
            String expressCode;
            String redisKey;
            String processingKey;
            
            if (key.endsWith(":processing")) {
                // 这是一个processing key，需要提取原始expressCode
                String baseKey = key.substring(0, key.length() - ":processing".length());
                expressCode = baseKey.substring("delivery:express:yunda:track:".length());
                redisKey = baseKey;
                processingKey = key;
            } else {
                // 这是一个原始key
                expressCode = key.substring("delivery:express:yunda:track:".length());
                redisKey = key;
                processingKey = key + ":processing";
            }
            
            String lockKey = "delivery:express:yunda:track:lock:" + expressCode;
            try {
                distributedLockTemplate.tryLock(lockKey, 10, 10, TimeUnit.SECONDS, () -> {
                    // 1) 恢复上次未处理完的记录：将processingKey中的数据回迁到源队列
                    while (true) {
                        String recovered = stringRedisTemplate.opsForList().rightPop(processingKey);
                        if (recovered == null) {
                            break;
                        }
                        stringRedisTemplate.opsForList().leftPush(redisKey, recovered);
                    }

                    // 2) 本次批次去重集合（按 operateTime + description）
                    Set<String> batchDedupSet = new HashSet<>();

                    // 3) 主处理循环：逐条搬运并处理，避免一次性删除导致的丢失
                    while (true) {
                        String trackJson = stringRedisTemplate.opsForList().rightPop(redisKey);
                        if (trackJson == null) {
                            break; // 源队列已空
                        }
                        // 先推入processing队列
                        stringRedisTemplate.opsForList().leftPush(processingKey, trackJson);

                        YundaExpressNotifyDTO dto;
                        try {
                            dto = JSON.parseObject(trackJson, YundaExpressNotifyDTO.class);
                        } catch (Exception parseEx) {
                            log.error("韵达轨迹JSON解析失败，expressCode: {}，原始：{}，错误：{}", expressCode, trackJson, parseEx.getMessage(), parseEx);
                            // 解析失败的脏数据直接移除，避免阻塞
                            stringRedisTemplate.opsForList().remove(processingKey, 1, trackJson);
                            continue;
                        }

                        String dedupKey = dto.getOperateTime() + "_" + dto.getDescription();
                        if (!batchDedupSet.add(dedupKey)) {
                            // 本批次重复，移除processing中的该条，跳过
                            stringRedisTemplate.opsForList().remove(processingKey, 1, trackJson);
                            continue;
                        }

                        try {
                            boolean processed = strategyManager.processOrder(dto);
                            if (!processed) {
                                log.warn("未能找到合适的处理策略，将尝试默认处理，快递单号：{}", dto.getExpressCode());
                            }
                            Express express = yundaExpressManage.updateExpressInfo(dto);
                            if (ObjectUtils.isNotNull(express) && express.getState().equals(ExpressStateEnum.RECEIVED.getValue())) {
                                expressBusiness.sendExpressReceived(express);
                            }
                            // 成功后移除processing中的当前元素
                            stringRedisTemplate.opsForList().remove(processingKey, 1, trackJson);
                        } catch (Exception e) {
                            // 失败则保留在processingKey中，供下次任务恢复重试
                            log.error("韵达轨迹处理失败，单号：{}，轨迹：{}，错误：{}", expressCode, trackJson, e.getMessage(), e);
                        }
                    }

                    log.info("韵达轨迹处理完成，单号：{}", expressCode);
                    return null;
                });
            } catch (Exception e) {
                log.error("韵达轨迹批量处理加锁失败，单号：{}，错误：{}", expressCode, e.getMessage(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * XXL-Job定时任务：韵达仓物流轨迹缓存查询
     * params为空：返回所有快递单号列表；params为单号时，返回该单号所有轨迹详情
     */
    @XxlJob("yundaExpressCacheQueryHandler")
    public ReturnT<String> yundaExpressCacheQueryHandler(String params) {
        if (params == null || params.trim().isEmpty()) {
            // 查询所有快递单号（包括原始和processing）
            Set<String> originalKeys = stringRedisTemplate.keys("delivery:express:yunda:track:*");
            Set<String> processingKeys = stringRedisTemplate.keys("delivery:express:yunda:track:*:processing");
            
            Set<String> allExpressCodes = new HashSet<>();
            
            // 处理原始key
            if (originalKeys != null) {
                for (String key : originalKeys) {
                    if (!key.endsWith(":processing")) {
                        allExpressCodes.add(key.substring("delivery:express:yunda:track:".length()));
                    }
                }
            }
            
            // 处理processing key
            if (processingKeys != null) {
                for (String key : processingKeys) {
                    String baseKey = key.substring(0, key.length() - ":processing".length());
                    allExpressCodes.add(baseKey.substring("delivery:express:yunda:track:".length()));
                }
            }
            
            if (allExpressCodes.isEmpty()) {
                XxlJobLogger.log("未找到任何韵达快递缓存单号");
                return ReturnT.SUCCESS;
            }
            
            List<String> expressCodes = new ArrayList<>(allExpressCodes);
            XxlJobLogger.log("共找到{}个韵达快递缓存单号: {}", expressCodes.size(), String.join(",", expressCodes));
            return ReturnT.SUCCESS;
        } else {
            // 查询指定单号的所有轨迹详情（包括原始和processing）
            String expressCode = params.trim();
            String redisKey = "delivery:express:yunda:track:" + expressCode;
            String processingKey = redisKey + ":processing";
            
            List<String> allTrackJsonList = new ArrayList<>();
            
            // 查询原始轨迹数据
            List<String> originalTrackList = stringRedisTemplate.opsForList().range(redisKey, 0, -1);
            if (originalTrackList != null && !originalTrackList.isEmpty()) {
                allTrackJsonList.addAll(originalTrackList);
                XxlJobLogger.log("单号{}原始缓存轨迹数量: {}", expressCode, originalTrackList.size());
            }
            
            // 查询processing中的轨迹数据
            List<String> processingTrackList = stringRedisTemplate.opsForList().range(processingKey, 0, -1);
            if (processingTrackList != null && !processingTrackList.isEmpty()) {
                allTrackJsonList.addAll(processingTrackList);
                XxlJobLogger.log("单号{}processing中轨迹数量: {}", expressCode, processingTrackList.size());
            }
            
            if (allTrackJsonList.isEmpty()) {
                XxlJobLogger.log("未找到单号{}的任何轨迹缓存记录", expressCode);
                return ReturnT.SUCCESS;
            }
            
            XxlJobLogger.log("单号{}总共缓存{}条轨迹记录: [{}]", expressCode, allTrackJsonList.size(), String.join(",", allTrackJsonList));
            return ReturnT.SUCCESS;
        }
    }
}
