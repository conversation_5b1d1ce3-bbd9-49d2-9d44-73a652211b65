package com.ets.delivery.application.common.consts.aftersalesreviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 售后审核领取状态枚举
 */
@Getter
@AllArgsConstructor
public enum AftersalesReviewsReceiveStatusEnum {

    NOT_RECEIVED(0, "未领取"),
    RECEIVED(1, "已领取");

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> map = Arrays.stream(values())
            .collect(Collectors.toMap(AftersalesReviewsReceiveStatusEnum::getValue, AftersalesReviewsReceiveStatusEnum::getDesc));
}
