package com.ets.delivery.application.common.consts.manualLogistics;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ManualLogisticsReasonEnum {

    REASON_KDDJ("KDDJ", "快递丢件"),
    REASON_CKFL("CKFL", "仓库发漏"),
    REASON_CKFC("CKFC", "仓库发错"),
    REASON_KDPS("KDPS", "快递破损"),
    REASON_XJDH("XJDH", "虚假单号（实际未发货）"),
    REASON_KDTH("KDTH", "快递退回"),
    REASON_WLTZ("WLTZ", "物流停滞（拦截发货）"),
    REASON_KFYY("KFYY", "客服原因"),
    REASON_XTYY("XTYY", "系统原因"),
    REASON_NBSQ("NBSQ", "内部申请"),
    REASON_YWSQ("YWSQ", "业务申请"),
    REASON_QT("QT", "其他");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        ManualLogisticsReasonEnum[] enums = ManualLogisticsReasonEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Stream.of(enums)
                .map(ManualLogisticsReasonEnum::getValue)
                .collect(Collectors.toList());
    }
}
