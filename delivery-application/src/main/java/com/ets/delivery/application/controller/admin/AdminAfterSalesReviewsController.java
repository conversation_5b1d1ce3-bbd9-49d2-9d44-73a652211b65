package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import com.ets.delivery.application.common.dto.aftersalesreviews.AdminAfReviewsReviewDTO;
import com.ets.delivery.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsBatchReceiveDTO;
import com.ets.delivery.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsDetailDTO;
import com.ets.delivery.application.common.dto.aftersalesreviews.AdminAfterSalesReviewsGetListDTO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsBatchReceiveVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsDetailVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsListVO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AdminAfterSalesReviewsSelectOptionVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核管理
 */
@RestController
@RequestMapping("/admin/aftersales-reviews")
public class AdminAfterSalesReviewsController {

    @Autowired
    private AfterSalesReviewsBusiness afterSalesReviewsBusiness;

    /**
     * 获取售后审核列表
     * @param dto 查询参数
     * @return 分页列表数据
     */
    @PostMapping("/get-list")
    public JsonResult<IPage<AdminAfterSalesReviewsListVO>> getList(@RequestBody @Valid AdminAfterSalesReviewsGetListDTO dto) {
        return JsonResult.ok(afterSalesReviewsBusiness.getList(dto));
    }

    /**
     * 获取售后审核详情
     * @param dto 查询参数
     * @return 详情数据
     */
    @CosSignWithWatermarkAnnotation
    @PostMapping("/get-detail")
    public JsonResult<AdminAfterSalesReviewsDetailVO> getDetail(@RequestBody @Valid AdminAfterSalesReviewsDetailDTO dto) {
        return JsonResult.ok(afterSalesReviewsBusiness.getDetail(dto));
    }

    /**
     * 审核
     * @param dto 审核参数
     * @return 审核结果
     */
    @PostMapping("/review")
    public JsonResult<Void> review(@RequestBody @Valid AdminAfReviewsReviewDTO dto) {
        afterSalesReviewsBusiness.review(dto);
        return JsonResult.ok();
    }

    /**
     * 售后审核下拉选项
     * @return 下拉选项
     */
    @PostMapping("/get-select-option")
    public JsonResult<AdminAfterSalesReviewsSelectOptionVO> getSelectOption() {
        return JsonResult.ok(afterSalesReviewsBusiness.getSelectOption());
    }

    /**
     * 批量领取售后审核单
     * @param dto 批量领取参数
     * @return 领取到的售后审核单列表
     */
    @PostMapping("/batch-receive")
    public JsonResult<AdminAfterSalesReviewsBatchReceiveVO> batchReceive(@RequestBody @Valid AdminAfterSalesReviewsBatchReceiveDTO dto) {
        return JsonResult.ok(afterSalesReviewsBusiness.batchReceive(dto));
    }


}
