package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.business.LogisticsExportBusiness;
import com.ets.delivery.application.app.business.LogisticsExpressImportBusiness;
import com.ets.delivery.application.common.dto.logistics.*;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressBatchSaveDTO;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportDetailDeleteDTO;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportListDTO;
import com.ets.delivery.application.common.vo.logistics.LogisticsDetailVO;
import com.ets.delivery.application.common.vo.logistics.LogisticsListVO;
import com.ets.delivery.application.common.vo.logistics.LogisticsLogVO;
import com.ets.delivery.application.common.vo.logisticsExpress.LogisticsExpressImportListVO;
import com.ets.delivery.application.common.vo.logisticsExpress.LogisticsExpressImportVO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/admin/logisticsOrder")
public class LogisticsOrderController {

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private LogisticsExpressImportBusiness logisticsExpressImportBusiness;

    @Autowired
    private LogisticsExportBusiness logisticsExportBusiness;

    @PostMapping("/getList")
    public JsonResult<IPage<LogisticsListVO>> getList(@RequestBody @Valid LogisticsListDTO listDTO) {
        return JsonResult.ok(logisticsBusiness.getList(listDTO));
    }

    @PostMapping("/getDetail")
    public JsonResult<LogisticsDetailVO> getDetail(@RequestBody @Valid LogisticsDetailDTO detailDTO) {
        return JsonResult.ok(logisticsBusiness.getDetail(detailDTO));
    }

    @PostMapping("/getLog")
    public JsonResult<IPage<LogisticsLogVO>> getLog(@RequestBody @Valid LogisticsLogDTO logDTO) {
        return JsonResult.ok(logisticsBusiness.getLog(logDTO));
    }

    @PostMapping("/cancel")
    public JsonResult<?> cancel(@RequestBody @Valid LogisticsCancelDTO cancelDTO) {
        logisticsBusiness.cancelLogistics(cancelDTO);
        return JsonResult.ok();
    }

    @PostMapping("/receive")
    public JsonResult<?> receive(@RequestBody @Valid LogisticsReceiveDTO receiveDTO) {
        logisticsBusiness.receiveLogistics(receiveDTO);
        return JsonResult.ok();
    }

    @PostMapping("/cancelReceive")
    public JsonResult<?> cancelReceive(@RequestBody @Valid LogisticsReceiveDTO receiveDTO) {
        logisticsBusiness.cancelReceiveLogistics(receiveDTO);
        return JsonResult.ok();
    }

    @PostMapping("/deliver")
    public JsonResult<?> deliver(@RequestBody @Valid LogisticsDeliverDTO deliverDTO) {
        logisticsBusiness.delivery(deliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/stopDeliver")
    public JsonResult<?> stopDeliver(@RequestBody @Valid LogisticsStopDeliverDTO stopDeliverDTO) {
        logisticsBusiness.stopDelivery(stopDeliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/resumeDeliver")
    public JsonResult<?> resumeDeliver(@RequestBody @Valid LogisticsResumeDeliverDTO resumeDeliverDTO) {
        logisticsBusiness.resumeDelivery(resumeDeliverDTO);
        return JsonResult.ok();
    }

    @PostMapping("/notify")
    public JsonResult<?> notifyResult(@RequestBody @Valid LogisticsNotifyDTO notifyDTO) {
        logisticsBusiness.notifyResult(notifyDTO);
        return JsonResult.ok();
    }

    @PostMapping("/menuList")
    public JsonResult<?> menuList() {
        return JsonResult.ok(logisticsBusiness.menuList());
    }

    @PostMapping("/import-file")
    public JsonResult<LogisticsExpressImportVO> importFile(MultipartFile file) {
        return JsonResult.ok(logisticsExpressImportBusiness.logisticsExpressImport(file));
    }

    @PostMapping("/get-import-data-list")
    public JsonResult<IPage<LogisticsExpressImportListVO>> getImportDataList(@RequestBody @Valid LogisticsExpressImportListDTO listDTO) {
        return JsonResult.ok(logisticsExpressImportBusiness.getImportDataList(listDTO));
    }

    @PostMapping("/batch-save")
    public JsonResult<?> batchSave(@RequestBody @Valid LogisticsExpressBatchSaveDTO batchSaveDTO) {
        logisticsExpressImportBusiness.batchSave(batchSaveDTO);
        return JsonResult.ok();
    }

    @PostMapping("/delete-import-record")
    public JsonResult<?> deleteImportRecord(@RequestBody @Valid LogisticsExpressImportDetailDeleteDTO deleteDTO) {
        logisticsExpressImportBusiness.deleteImportRecord(deleteDTO);
        return JsonResult.ok();
    }

    /**
     * 导出接口：按仓库、状态正常、发货状态待处理，限制条数（最多10000）
     * 表头：发货流水号、订单号、发货商品、收货地址、收货人姓名、收货人联系电话
     */
    @PostMapping("/export-data")
    public void exportData(@RequestBody @Valid LogisticsExportDTO dto,
                           HttpServletResponse response) throws IOException {
        logisticsExportBusiness.exportWaitData(dto, response);
    }
}