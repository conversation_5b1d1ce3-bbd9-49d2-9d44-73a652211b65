package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.RejectExpressImportBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageRecordBusiness;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressExportDTO;
import com.ets.delivery.application.common.dto.rejectExpress.RejectExpressImportListDTO;
import com.ets.delivery.application.common.dto.storage.*;
import com.ets.delivery.application.common.vo.rejectExpress.RejectExpressImportListVO;
import com.ets.delivery.application.common.vo.rejectExpress.RejectExpressImportVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordAdminListVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordAdminLogVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordDetailVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordUploadImgVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/admin/storageRecord")
public class StorageRecordController {

    @Autowired
    private RejectExpressImportBusiness importBusiness;

    @Autowired
    private StorageRecordBusiness storageRecordBusiness;

    @RequestMapping("/getList")
    @CosSignAnnotation
    public JsonResult<IPage<StorageRecordAdminListVO>> getList(@RequestBody @Valid StorageRecordAdminListDTO listDTO) {
        return JsonResult.ok(storageRecordBusiness.getList(listDTO));
    }

    @RequestMapping("/getDetail")
    @CosSignAnnotation
    public JsonResult<StorageRecordDetailVO> getDetail(@RequestBody @Valid StorageRecordDetailDTO detailDTO) {
        return JsonResult.ok(storageRecordBusiness.getDetail(detailDTO));
    }

    @RequestMapping("/create")
    public JsonResult<?> create(@RequestBody @Valid StorageRecordAdminCreateDTO createDTO) {
        storageRecordBusiness.create(createDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid StorageRecordAdminEditDTO editDTO) {
        storageRecordBusiness.edit(editDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/getLog")
    public JsonResult<IPage<StorageRecordAdminLogVO>> getLog(@RequestBody @Valid StorageRecordAdminLogDTO logDTO) {
        return JsonResult.ok(storageRecordBusiness.getLog(logDTO));
    }

    @RequestMapping("/uploadImg")
    public JsonResult<StorageRecordUploadImgVO> uploadImg(MultipartFile file) {
        return JsonResult.ok(storageRecordBusiness.uploadImg(file));
    }

    @RequestMapping("/importFile")
    public JsonResult<RejectExpressImportVO> importFile(MultipartFile file) {
        return JsonResult.ok(importBusiness.importFile(file));
    }

    @RequestMapping("/getImportDataList")
    public JsonResult<IPage<RejectExpressImportListVO>> getImportDataList(@RequestBody @Valid RejectExpressImportListDTO listDTO) {
        return JsonResult.ok(importBusiness.getImportDataList(listDTO));
    }

    @RequestMapping("/exportFile")
    public void exportFile(@RequestBody @Valid RejectExpressExportDTO exportDTO, HttpServletResponse response) {
        importBusiness.exportFile(exportDTO, response);
    }

    @PostMapping("/getOptions")
    public JsonResult<HashMap<String, List<?>>> getOptions() {

        return JsonResult.ok(storageRecordBusiness.getOptions());
    }

}
