# 序列号处理队列配置示例
ets-config:
  rocketmq:
    serial:
      # RocketMQ NameServer地址
      nameServerAddr: ${ROCKETMQ_NAME_SERVER:localhost:9876}
      # 消费者组名
      groupName: ${ROCKETMQ_SERIAL_GROUP:delivery-serial-group}
      # Topic名称
      topic: ${ROCKETMQ_SERIAL_TOPIC:delivery-serial-topic}
      # 队列名称
      queueName: ${ROCKETMQ_SERIAL_QUEUE:serial-process-queue}
      # 重试次数
      retryTimes: ${ROCKETMQ_SERIAL_RETRY:3}
      # 消费者最小线程数
      consumeThreadMin: ${ROCKETMQ_SERIAL_THREAD_MIN:5}
      # 消费者最大线程数
      consumeThreadMax: ${ROCKETMQ_SERIAL_THREAD_MAX:20}
