package com.ets.delivery.application;

import com.ets.delivery.application.app.business.serial.SerialBusiness;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * SerialBusiness 测试类
 * 测试序列号处理相关功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class SerialBusinessTest {

    /**
     * 测试被移除序列号的处理逻辑
     * 这个测试用于验证 processRemovedSerials 方法的基本逻辑
     */
    @Test
    public void testProcessRemovedSerialsLogic() {
        // 模拟原有序列号列表
        List<String> originalSerialList = Arrays.asList(
            "1234567890123456", 
            "1234567890123457", 
            "1234567890123458", 
            "1234567890123459"
        );
        
        // 模拟新序列号列表（移除了两个序列号）
        List<String> newSerialList = Arrays.asList(
            "1234567890123456", 
            "1234567890123458"
        );
        
        // 预期被移除的序列号
        List<String> expectedRemovedSerials = Arrays.asList(
            "1234567890123457", 
            "1234567890123459"
        );
        
        // 手动计算被移除的序列号（模拟业务逻辑）
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        // 验证结果
        assert actualRemovedSerials.size() == 2 : "应该有2个被移除的序列号";
        assert actualRemovedSerials.containsAll(expectedRemovedSerials) : "被移除的序列号不匹配";
        
        System.out.println("原有序列号: " + originalSerialList);
        System.out.println("新序列号: " + newSerialList);
        System.out.println("被移除的序列号: " + actualRemovedSerials);
        System.out.println("测试通过：被移除序列号处理逻辑正确");
    }
    
    /**
     * 测试边界情况：原有序列号为空
     */
    @Test
    public void testProcessRemovedSerialsWithEmptyOriginalList() {
        List<String> originalSerialList = Arrays.asList();
        List<String> newSerialList = Arrays.asList("1234567890123456");
        
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        assert actualRemovedSerials.isEmpty() : "原有序列号为空时，不应该有被移除的序列号";
        System.out.println("边界测试通过：原有序列号为空的情况处理正确");
    }
    
    /**
     * 测试边界情况：新序列号为空
     */
    @Test
    public void testProcessRemovedSerialsWithEmptyNewList() {
        List<String> originalSerialList = Arrays.asList("1234567890123456", "1234567890123457");
        List<String> newSerialList = Arrays.asList();
        
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        assert actualRemovedSerials.size() == 2 : "新序列号为空时，所有原有序列号都应该被移除";
        assert actualRemovedSerials.containsAll(originalSerialList) : "应该包含所有原有序列号";
        System.out.println("边界测试通过：新序列号为空的情况处理正确");
    }
    
    /**
     * 测试边界情况：序列号完全相同
     */
    @Test
    public void testProcessRemovedSerialsWithSameLists() {
        List<String> originalSerialList = Arrays.asList("1234567890123456", "1234567890123457");
        List<String> newSerialList = Arrays.asList("1234567890123456", "1234567890123457");

        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());

        assert actualRemovedSerials.isEmpty() : "序列号完全相同时，不应该有被移除的序列号";
        System.out.println("边界测试通过：序列号完全相同的情况处理正确");
    }

    /**
     * 测试序列号去重逻辑
     */
    @Test
    public void testSerialDeduplication() {
        // 包含重复序列号的列表
        List<String> serialListWithDuplicates = Arrays.asList(
            "1234567890123456",
            "1234567890123457",
            "1234567890123456", // 重复
            "1234567890123458",
            "1234567890123457"  // 重复
        );

        // 手动去重
        List<String> deduplicatedList = serialListWithDuplicates.stream()
                .distinct()
                .collect(java.util.stream.Collectors.toList());

        // 验证去重结果
        assert deduplicatedList.size() == 3 : "去重后应该有3个唯一序列号";
        assert deduplicatedList.contains("1234567890123456") : "应该包含第一个序列号";
        assert deduplicatedList.contains("1234567890123457") : "应该包含第二个序列号";
        assert deduplicatedList.contains("1234567890123458") : "应该包含第三个序列号";

        System.out.println("原始序列号（含重复）: " + serialListWithDuplicates);
        System.out.println("去重后序列号: " + deduplicatedList);
        System.out.println("去重测试通过：重复序列号处理正确");
    }

    /**
     * 测试操作时间字符串转LocalDateTime功能
     * 使用反射调用私有方法进行测试
     */
    @Test
    public void testParseOperateTime() throws Exception {
        SerialBusiness serialBusiness = new SerialBusiness();

        // 使用反射获取私有方法
        Method parseOperateTimeMethod = SerialBusiness.class.getDeclaredMethod("parseOperateTime", String.class);
        parseOperateTimeMethod.setAccessible(true);

        // 测试正常的日期时间格式
        String dateTimeStr = "2025-09-17 14:30:00";
        LocalDateTime result1 = (LocalDateTime) parseOperateTimeMethod.invoke(serialBusiness, dateTimeStr);
        LocalDateTime expected1 = LocalDateTime.of(2025, 9, 17, 14, 30, 0);
        assert result1.equals(expected1) : "日期时间格式解析失败";
        System.out.println("测试通过：日期时间格式 '" + dateTimeStr + "' 解析为 " + result1);

        // 测试只有日期的格式
        String dateStr = "2025-09-17";
        LocalDateTime result2 = (LocalDateTime) parseOperateTimeMethod.invoke(serialBusiness, dateStr);
        LocalDateTime expected2 = LocalDateTime.of(2025, 9, 17, 0, 0, 0);
        assert result2.equals(expected2) : "日期格式解析失败";
        System.out.println("测试通过：日期格式 '" + dateStr + "' 解析为 " + result2);

        // 测试空字符串
        LocalDateTime result3 = (LocalDateTime) parseOperateTimeMethod.invoke(serialBusiness, "");
        assert result3 != null : "空字符串应该返回当前时间";
        System.out.println("测试通过：空字符串返回当前时间 " + result3);

        // 测试null值
        LocalDateTime result4 = (LocalDateTime) parseOperateTimeMethod.invoke(serialBusiness, (String) null);
        assert result4 != null : "null值应该返回当前时间";
        System.out.println("测试通过：null值返回当前时间 " + result4);

        // 测试无效格式
        String invalidStr = "invalid-date-format";
        LocalDateTime result5 = (LocalDateTime) parseOperateTimeMethod.invoke(serialBusiness, invalidStr);
        assert result5 != null : "无效格式应该返回当前时间";
        System.out.println("测试通过：无效格式 '" + invalidStr + "' 返回当前时间 " + result5);

        System.out.println("所有操作时间解析测试通过！");
    }
}
